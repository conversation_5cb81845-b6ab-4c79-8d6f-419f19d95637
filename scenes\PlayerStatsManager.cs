using Godot;
using System;

public partial class PlayerStatsManager : Node
{
	public static PlayerStatsManager Instance { get; private set; }

	[Export] public int MaxEnergy { get; set; } = 10;
	[Export] public int MaxFood { get; set; } = 30;
	[Export] public int MaxWater { get; set; } = 30;
	[Export] public int MaxHealth { get; set; } = 10;

	public int CurrentEnergy { get; private set; } = 10;
	public int CurrentFood { get; private set; } = 30;
	public int CurrentWater { get; private set; } = 30;
	public int CurrentHealth { get; private set; } = 10;

	private int _actionCount = 0;
	private Timer _energyRestoreTimer;
	private Timer _starvationTimer;
	private float _energyRestoreInterval = 1.0f;
	private float _minEnergyRestoreInterval = 0.1f;
	private bool _isEnergyRestoring = false;
	private ColorRect _dangerOverlay;

	[Signal] public delegate void EnergyChangedEventHandler(int current, int max);
	[Signal] public delegate void FoodChangedEventHandler(int current, int max);
	[Signal] public delegate void WaterChangedEventHandler(int current, int max);
	[Signal] public delegate void HealthChangedEventHandler(int current, int max);
	[Signal] public delegate void PlayerDiedEventHandler();

	public override void _Ready()
	{
		if (Instance == null)
		{
			Instance = this;
		}
		else
		{
			QueueFree();
			return;
		}

		SetupTimers();
		SetupDangerOverlay();
		ConnectSignals();
		LoadStats();

		EmitSignal(SignalName.EnergyChanged, CurrentEnergy, MaxEnergy);
		EmitSignal(SignalName.FoodChanged, CurrentFood, MaxFood);
		EmitSignal(SignalName.WaterChanged, CurrentWater, MaxWater);
		EmitSignal(SignalName.HealthChanged, CurrentHealth, MaxHealth);
	}

	private void SetupTimers()
	{
		_energyRestoreTimer = new Timer();
		_energyRestoreTimer.WaitTime = 5.0f;
		_energyRestoreTimer.OneShot = true;
		_energyRestoreTimer.Timeout += StartEnergyRestore;
		AddChild(_energyRestoreTimer);

		_starvationTimer = new Timer();
		_starvationTimer.WaitTime = 30.0f;
		_starvationTimer.OneShot = true;
		_starvationTimer.Timeout += OnStarvationTimeout;
		AddChild(_starvationTimer);
	}

	private void SetupDangerOverlay()
	{
		_dangerOverlay = new ColorRect();
		_dangerOverlay.Color = new Color(1.0f, 0.0f, 0.0f, 0.15f);
		_dangerOverlay.MouseFilter = Control.MouseFilterEnum.Ignore;
		_dangerOverlay.SetAnchorsPreset(Control.LayoutPreset.FullRect);
		_dangerOverlay.Visible = false;

		var currentScene = GetTree().CurrentScene;
		if (currentScene != null)
		{
			currentScene.AddChild(_dangerOverlay);
		}
		else
		{
			GD.PrintErr("PlayerStatsManager: Could not get current scene for danger overlay!");
		}
	}

	private void ConnectSignals()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.ToolUsed += OnToolUsed;
		}
	}

	public void OnToolUsed()
	{
		UseEnergy(1);
		_actionCount++;

		if (_actionCount % 2 == 0)
		{
			UseFood(1);
		}

		if (_actionCount % 3 == 0)
		{
			UseWater(1);
		}

		ResetEnergyRestoreTimer();
	}

	private void UseEnergy(int amount)
	{
		CurrentEnergy = Math.Max(0, CurrentEnergy - amount);
		EmitSignal(SignalName.EnergyChanged, CurrentEnergy, MaxEnergy);
	}

	private void UseFood(int amount)
	{
		CurrentFood = Math.Max(0, CurrentFood - amount);
		EmitSignal(SignalName.FoodChanged, CurrentFood, MaxFood);
		UpdateDangerOverlay();
		CheckStarvation();
	}

	private void UseWater(int amount)
	{
		CurrentWater = Math.Max(0, CurrentWater - amount);
		EmitSignal(SignalName.WaterChanged, CurrentWater, MaxWater);
		UpdateDangerOverlay();
		CheckStarvation();
	}

	public void AddFood(int amount)
	{
		CurrentFood = Math.Min(MaxFood, CurrentFood + amount);
		EmitSignal(SignalName.FoodChanged, CurrentFood, MaxFood);
		UpdateDangerOverlay();
		CheckStarvation();
	}

	public void AddWater(int amount)
	{
		CurrentWater = Math.Min(MaxWater, CurrentWater + amount);
		EmitSignal(SignalName.WaterChanged, CurrentWater, MaxWater);
		UpdateDangerOverlay();
		CheckStarvation();
	}

	public void AddHealth(int amount)
	{
		CurrentHealth = Math.Min(MaxHealth, CurrentHealth + amount);
		EmitSignal(SignalName.HealthChanged, CurrentHealth, MaxHealth);
	}

	private void ResetEnergyRestoreTimer()
	{
		_isEnergyRestoring = false;
		_energyRestoreTimer.Stop();
		_energyRestoreTimer.Start();
	}

	private void StartEnergyRestore()
	{
		if (CurrentEnergy < MaxEnergy)
		{
			_isEnergyRestoring = true;
			_energyRestoreInterval = 1.0f;
			RestoreEnergyStep();
		}
	}

	private void RestoreEnergyStep()
	{
		if (!_isEnergyRestoring || CurrentEnergy >= MaxEnergy)
			return;

		CurrentEnergy = Math.Min(MaxEnergy, CurrentEnergy + 1);
		EmitSignal(SignalName.EnergyChanged, CurrentEnergy, MaxEnergy);

		if (CurrentEnergy < MaxEnergy)
		{
			_energyRestoreInterval = Math.Max(_minEnergyRestoreInterval, _energyRestoreInterval - 0.1f);
			GetTree().CreateTimer(_energyRestoreInterval).Timeout += RestoreEnergyStep;
		}
	}

	private void UpdateDangerOverlay()
	{
		if (_dangerOverlay == null) return;

		bool showDanger = CurrentFood == 0 || CurrentWater == 0;
		_dangerOverlay.Visible = showDanger;

		if (showDanger)
		{
			float alpha = (CurrentFood == 0 && CurrentWater == 0) ? 0.25f : 0.15f;
			_dangerOverlay.Color = new Color(1.0f, 0.0f, 0.0f, alpha);
		}
	}

	private void CheckStarvation()
	{
		if (CurrentFood == 0 || CurrentWater == 0)
		{
			_starvationTimer.Start();
		}
		else
		{
			_starvationTimer.Stop();
		}
	}

	private void OnStarvationTimeout()
	{
		if (CurrentFood == 0 || CurrentWater == 0)
		{
			PlayerDie();
		}
	}

	private void PlayerDie()
	{
		GD.Print("Player died from starvation/dehydration!");
		EmitSignal(SignalName.PlayerDied);
	}

	public float GetSpeedModifier()
	{
		if (CurrentFood == 0 && CurrentWater == 0)
			return 0.4f;
		else if (CurrentFood == 0 || CurrentWater == 0)
			return 0.6f;
		else
			return 1.0f;
	}

	public float GetAnimationSpeedModifier()
	{
		return GetSpeedModifier();
	}

	public void ConsumeBerry()
	{
		AddFood(5);
		AddWater(3);
		GD.Print("Consumed berry: +5 food, +3 water");
	}

	public void ConsumeRawRabbitLeg()
	{
		AddFood(4);
		GD.Print("Consumed raw rabbit leg: +4 food");
	}

	public void ConsumeCookedRabbitLeg()
	{
		AddHealth(10);
		GD.Print("Consumed cooked rabbit leg: +10 health");
	}

	private void LoadStats()
	{
		var rm = ResourcesManager.Instance;
		if (rm != null)
		{
			CurrentHealth = (int)rm.GetHealth();
			CurrentEnergy = (int)rm.GetEnergy();
			CurrentFood = (int)rm.GetFood();
			CurrentWater = (int)rm.GetWater();
			GD.Print($"PlayerStatsManager: Loaded stats - Health: {CurrentHealth}, Energy: {CurrentEnergy}, Food: {CurrentFood}, Water: {CurrentWater}");
		}
	}

	private void SaveStats()
	{
		var rm = ResourcesManager.Instance;
		if (rm != null)
		{
			rm.SetHealth(CurrentHealth);
			rm.SetEnergy(CurrentEnergy);
			rm.SetFood(CurrentFood);
			rm.SetWater(CurrentWater);
		}
	}

	public override void _ExitTree()
	{
		SaveStats();

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.ToolUsed -= OnToolUsed;
		}
	}
}
