using Godot;
using System;
using System.Collections.Generic;

public partial class Campfire : Node2D, IDestroyableObject
{
	private const int TILE_SIZE = 16;
	private const int BUILDING_WIDTH = 1;
	private const int BUILDING_HEIGHT = 2;
	private const int MAX_HEALTH = 20;

	[Export] public ObjectType BuildingType { get; set; } = ObjectType.Anvil;

	private Sprite2D _campfireSprite;
	private Sprite2D _craftingResourceSprite;
	private ProgressBar _hpBar;
	private ProgressBarVertical _craftingProgressBar;
	private AnimationPlayer _animationPlayer;
	private Area2D _playerDetector;
	private StaticBody2D _staticBody;
	private CampfireMenu _campfireMenu;

	private Vector2I _topLeftTilePosition;
	private bool _isPlaced = false;
	private bool _isPlayerInRange = false;
	private bool _isBeingDestroyed = false;
	private int _currentHealth = MAX_HEALTH;
	private string _saveId = "";

	private ResourceType _selectedCraftingResource = ResourceType.None;
	private int _amountToProduce = 1;
	private Timer _cookingTimer;
	private bool _isCooking = false;
	private int _currentCookingProgress = 0;

	private readonly Dictionary<ResourceType, int> CookingTimeSeconds = new()
	{
		{ ResourceType.CookedRabbitLeg, 10 }
	};

	private readonly Dictionary<ResourceType, Dictionary<ResourceType, int>> CookingRecipes = new()
	{
		{
			ResourceType.CookedRabbitLeg,
			new Dictionary<ResourceType, int>
			{
				{ ResourceType.RawRabbitLeg, 1 },
				{ ResourceType.Wood, 1 }
			}
		}
	};

	private CustomDataLayerManager _customDataManager;
	private Color _normalColor = Colors.White;
	private CampfireLight _campfireLight;

	public override void _Ready()
	{
		_campfireSprite = GetNode<Sprite2D>("Campfire");
		_craftingResourceSprite = GetNode<Sprite2D>("CraftingResource");
		_hpBar = GetNode<ProgressBar>("ProgressBar");
		_craftingProgressBar = GetNode<ProgressBarVertical>("ProgressBarVertical");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_playerDetector = GetNode<Area2D>("PlayerDetector");
		_staticBody = GetNode<StaticBody2D>("StaticBody2D");
		_campfireMenu = GetNode<CampfireMenu>("CampfireMenu");

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");

		if (_campfireMenu != null)
		{
			_campfireMenu.SetCampfire(this);
		}

		if (_playerDetector != null)
		{
			// Set collision mask to detect PlayerDetector (layer 3)
			_playerDetector.CollisionMask = 4; // Detect layer 3 (bit 2 = 4)
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}

		if (_hpBar != null)
		{
			_hpBar.Hide();
		}

		if (_craftingProgressBar != null)
		{
			_craftingProgressBar.Hide();
		}

		_cookingTimer = new Timer();
		AddChild(_cookingTimer);
		_cookingTimer.WaitTime = 1.0f;
		_cookingTimer.Timeout += OnCookingTimerTimeout;

		if (_isPlaced)
		{
			StartCampfireAnimation();
		}

		UpdateHPBar();
		UpdateCraftingResourceDisplay();

		// Initialize campfire light component
		_campfireLight = new CampfireLight();
		AddChild(_campfireLight);
		GD.Print("CampfireLight component added to campfire");
	}

	public void PlaceBuilding(Vector2I topLeftTilePosition, CustomDataLayerManager customDataManager)
	{
		_topLeftTilePosition = topLeftTilePosition;
		_customDataManager = customDataManager;

		float centerX = (_topLeftTilePosition.X + 0.5f) * TILE_SIZE;
		float centerY = (_topLeftTilePosition.Y + 1.0f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
				_customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}
		_isPlaced = true;

		if (_campfireSprite != null)
		{
			_campfireSprite.Modulate = _normalColor;
		}

		StartCampfireAnimation();

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			var buildingData = new BuildingData
			{
				Id = _saveId.Length > 0 ? _saveId : System.Guid.NewGuid().ToString(),
				BuildingType = "Campfire",
				TopLeftX = _topLeftTilePosition.X,
				TopLeftY = _topLeftTilePosition.Y,
				BuildingId = (int)ObjectType.Anvil,
				CurrentHealth = _currentHealth,
				SelectedCraftingResource = (int)_selectedCraftingResource,
				CraftingProgress = _currentCookingProgress
			};

			_saveId = buildingData.Id;
			resourcesManager.GameData.WorldData.Buildings.Add(buildingData);
		}

		CommonSignals.Instance?.EmitCampfireBuilt();
	}

	private void StartCampfireAnimation()
	{
		if (_animationPlayer != null)
		{
			_animationPlayer.Play("AnimateCampfire");
		}
	}

	public void DestroyBuilding()
	{
		if (_customDataManager == null || !_isPlaced || _isBeingDestroyed) return;
		_isBeingDestroyed = true;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
				_customDataManager.ClearObjectPlaced(tilePos);
			}
		}

		if (!string.IsNullOrEmpty(_saveId))
		{
			ResourcesManager.Instance?.RemoveBuildingById(_saveId);
		}

		QueueFree();
	}

	public bool IsPlaced()
	{
		return _isPlaced;
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;
		UpdateHPBar();

		if (_currentHealth <= 0)
		{
			DestroyBuilding();
		}
	}

	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		float healthPercentage = (float)_currentHealth / MAX_HEALTH;

		if (_currentHealth >= MAX_HEALTH)
		{
			_hpBar.Hide();
		}
		else
		{
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || !_isPlaced) return;

		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.R)
			{
				OpenCampfireMenu();
			}
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		_isPlayerInRange = true;
		GD.Print("Campfire: Player in range - press 'R' to cook");
	}

	private void OnPlayerExited(Area2D area)
	{
		_isPlayerInRange = false;
	}

	private void OpenCampfireMenu()
	{
		if (_campfireMenu != null)
		{
			_campfireMenu.OpenMenu();
		}
	}

	public void StartCooking(ResourceType resourceType, int amount)
	{
		if (_isCooking) return;

		_selectedCraftingResource = resourceType;
		_amountToProduce = amount;
		_currentCookingProgress = 0;
		_isCooking = true;

		UpdateCraftingResourceDisplay();
		_cookingTimer.Start();
	}

	private void OnCookingTimerTimeout()
	{
		if (!_isCooking || _selectedCraftingResource == ResourceType.None) return;

		_currentCookingProgress++;
		UpdateCraftingProgress();

		if (CookingTimeSeconds.TryGetValue(_selectedCraftingResource, out int requiredTime))
		{
			if (_currentCookingProgress >= requiredTime)
			{
				CompleteCooking();
			}
		}
	}

	private void UpdateCraftingResourceDisplay()
	{
		if (_craftingResourceSprite == null) return;

		if (_selectedCraftingResource == ResourceType.None)
		{
			_craftingResourceSprite.Texture = null;
			_craftingResourceSprite.Visible = false;
		}
		else
		{
			var textureManager = TextureManager.Instance;
			if (textureManager != null)
			{
				var texture = textureManager.GetResourceTexture(_selectedCraftingResource);
				_craftingResourceSprite.Texture = texture;
				_craftingResourceSprite.Visible = true;
			}
		}
	}

	private void UpdateCraftingProgress()
	{
		if (_craftingProgressBar == null || _selectedCraftingResource == ResourceType.None) return;

		if (CookingTimeSeconds.TryGetValue(_selectedCraftingResource, out int requiredTime))
		{
			if (_currentCookingProgress > 0)
			{
				_craftingProgressBar.Show();
				float progressPercentage = (float)_currentCookingProgress / requiredTime;
				_craftingProgressBar.SetProgress(progressPercentage);
			}
			else
			{
				_craftingProgressBar.Hide();
			}
		}
	}

	private void CompleteCooking()
	{
		_cookingTimer.Stop();
		_isCooking = false;

		SpawnCookedItem();

		_amountToProduce--;
		if (_amountToProduce > 0)
		{
			_currentCookingProgress = 0;
			_isCooking = true;
			_cookingTimer.Start();
		}
		else
		{
			_selectedCraftingResource = ResourceType.None;
			_currentCookingProgress = 0;
			UpdateCraftingResourceDisplay();
			_craftingProgressBar?.Hide();
		}
	}

	private void SpawnCookedItem()
	{
		Vector2 spawnPosition = GlobalPosition + new Vector2(
			GD.RandRange(-10, 10),
			GD.RandRange(-10, 10)
		);
		DroppedResource.SpawnResource(spawnPosition, _selectedCraftingResource, 1);

		// Emit signal for region unlocking and tutorial NPC
		if (_selectedCraftingResource == ResourceType.CookedRabbitLeg)
		{
			CommonSignals.Instance?.EmitRabbitLegCooked();
		}
	}

	public bool CanAffordRecipe(ResourceType recipeType, int amount)
	{
		if (!CookingRecipes.TryGetValue(recipeType, out var recipe)) return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		foreach (var ingredient in recipe)
		{
			if (!resourcesManager.HasResource(ingredient.Key, ingredient.Value * amount))
			{
				return false;
			}
		}

		return true;
	}

	public bool ConsumeRecipeResources(ResourceType recipeType, int amount)
	{
		if (!CookingRecipes.TryGetValue(recipeType, out var recipe)) return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		foreach (var ingredient in recipe)
		{
			if (!resourcesManager.RemoveResource(ingredient.Key, ingredient.Value * amount))
			{
				return false;
			}
		}

		return true;
	}

	public Vector2I GetTilePosition()
	{
		return _topLeftTilePosition;
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		var distance = _topLeftTilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MAX_HEALTH));
		UpdateHPBar();
	}

	public Vector2I GetTopLeftTilePosition()
	{
		return _topLeftTilePosition;
	}

	public void SetTilePosition(Vector2I position)
	{
		_topLeftTilePosition = position;
		float centerX = (_topLeftTilePosition.X + 0.5f) * TILE_SIZE;
		float centerY = (_topLeftTilePosition.Y + 1.0f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);
	}

	public bool CanBePlacedAt(Vector2I topLeftTile)
	{
		if (_customDataManager == null) return false;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = topLeftTile + new Vector2I(x, y);
				var tileData = _customDataManager.GetTileData(tilePos);

				if (tileData.ObjectTypePlaced != ObjectTypePlaced.None || tileData.Region <= 0)
				{
					return false;
				}
			}
		}

		return true;
	}

	public void SetPlacementFeedback(bool canPlace)
	{
		if (_campfireSprite != null)
		{
			_campfireSprite.Modulate = canPlace ? Colors.White : Colors.Red;
		}
	}

	public void PlaceBuilding()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		if (!resourcesManager.HasResource(ResourceType.Wood, 5) ||
			!resourcesManager.HasResource(ResourceType.Stone, 2))
		{
			GD.Print("Campfire: Not enough resources!");
			return;
		}

		if (!resourcesManager.RemoveResource(ResourceType.Wood, 5) ||
			!resourcesManager.RemoveResource(ResourceType.Stone, 2))
		{
			GD.Print("Campfire: Failed to consume resources!");
			return;
		}

		PlaceBuilding(_topLeftTilePosition, _customDataManager);
	}

	public void LoadFromSaveData(BuildingData buildingData)
	{
		_saveId = buildingData.Id;
		_topLeftTilePosition = new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY);
		BuildingType = (ObjectType)buildingData.BuildingId;

		float centerX = (_topLeftTilePosition.X + 0.5f) * TILE_SIZE;
		float centerY = (_topLeftTilePosition.Y + 1.0f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);

		_isPlaced = true;
		if (_campfireSprite != null)
		{
			_campfireSprite.Modulate = _normalColor;
		}

		_currentHealth = buildingData.CurrentHealth;
		_selectedCraftingResource = (ResourceType)buildingData.SelectedCraftingResource;
		_currentCookingProgress = buildingData.CraftingProgress;

		UpdateHPBar();
		StartCampfireAnimation();

		if (_selectedCraftingResource != ResourceType.None || _currentCookingProgress > 0)
		{
			CallDeferred(nameof(UpdateCraftingResourceDisplay));
		}
	}
}
