using Godot;
using System.Collections.Generic;

public partial class Chest : Node2D
{
	public enum ChestType
	{
		WoodenChest = 0,
		StoneChest = 1
	}

	[Export] public ChestType CurrentChestType { get; set; } = ChestType.WoodenChest;
	[Export] public Texture2D WoodenChestTexture { get; set; }
	[Export] public Texture2D StoneChestTexture { get; set; }
	[Export] public int GoldAmount { get; set; } = 50;
	public List<ResourceReward> ResourceRewards { get; set; } = new List<ResourceReward>();

	public string Id { get; set; } = System.Guid.NewGuid().ToString();

	private Sprite2D _sprite;
	private AnimationPlayer _animationPlayer;
	private Area2D _playerDetection;
	private bool _isPlayerInRange = false;
	private bool _isOpened = false;

	public override void _Ready()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_playerDetection = GetNode<Area2D>("PlayerDetection");

		if (_sprite == null || _animationPlayer == null || _playerDetection == null)
		{
			GD.PrintErr("Chest: Required child nodes not found!");
			return;
		}

		SetupPlayerDetection();
		SetChestTexture();
		RegisterWithGameData();
	}

	public override void _ExitTree()
	{
		if (_playerDetection != null)
		{
			_playerDetection.AreaEntered -= OnPlayerEntered;
			_playerDetection.AreaExited -= OnPlayerExited;
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || _isOpened) return;

		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.R)
			{
				TryOpenChest();
			}
		}
	}

	private void SetupPlayerDetection()
	{
		_playerDetection.CollisionMask = 4;
		_playerDetection.AreaEntered += OnPlayerEntered;
		_playerDetection.AreaExited += OnPlayerExited;
	}

	private void SetChestTexture()
	{
		if (_sprite == null) return;

		switch (CurrentChestType)
		{
			case ChestType.WoodenChest:
				if (WoodenChestTexture != null)
					_sprite.Texture = WoodenChestTexture;
				break;
			case ChestType.StoneChest:
				if (StoneChestTexture != null)
					_sprite.Texture = StoneChestTexture;
				break;
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
			GD.Print("Chest: Player in range - press 'R' to open");
		}
	}

	private void OnPlayerExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
		}
	}

	private void TryOpenChest()
	{
		if (_isOpened) return;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null)
		{
			GD.PrintErr("Chest: ResourcesManager not found!");
			return;
		}

		ResourceType requiredKey = GetRequiredKey();
		if (!resourcesManager.HasResource(requiredKey, 1))
		{
			GD.Print($"Chest: Player lacks required key: {requiredKey}");
			return;
		}

		// Only disable movement if we're actually going to open the chest
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		if (!resourcesManager.RemoveResource(requiredKey, 1))
		{
			GD.Print($"Chest: Failed to consume key: {requiredKey}");
			// Re-enable movement if key consumption fails
			CommonSignals.Instance?.EmitPlayerMovementEnabled(true);
			return;
		}

		OpenChest();
	}

	private ResourceType GetRequiredKey()
	{
		return CurrentChestType switch
		{
			ChestType.WoodenChest => ResourceType.WoodenKey,
			ChestType.StoneChest => ResourceType.WoodenKey,
			_ => ResourceType.WoodenKey
		};
	}

	private void OpenChest()
	{
		_isOpened = true;
		GD.Print($"Chest: Opening {CurrentChestType} chest!");

		if (_animationPlayer.HasAnimation("OpenChest"))
		{
			_animationPlayer.Play("OpenChest");
			_animationPlayer.AnimationFinished += OnOpenAnimationFinished;
		}
		else
		{
			SpawnRewards();
		}
	}

	private void OnOpenAnimationFinished(StringName animName)
	{
		if (animName == "OpenChest")
		{
			_animationPlayer.AnimationFinished -= OnOpenAnimationFinished;
			SpawnRewards();
		}
	}

	private void SpawnRewards()
	{
		SpawnGold();
		SpawnResources();

		// Re-enable player movement after chest is opened and rewards are spawned
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		DestroyChest();
	}

	private void SpawnGold()
	{
		if (GoldAmount <= 0) return;

		for (int i = 0; i < GoldAmount; i++)
		{
			Vector2 offset = new Vector2(
				(float)(GD.Randf() - 0.5f) * 16.0f,
				(float)(GD.Randf() - 0.5f) * 16.0f
			);
			Vector2 spawnPosition = GlobalPosition + offset;

			var resourcesManager = ResourcesManager.Instance;
			if (resourcesManager != null)
			{
				resourcesManager.ModifyMoney(1);
			}
		}
		GD.Print($"Chest: Added {GoldAmount} gold to player inventory");
	}

	private void SpawnResources()
	{
		foreach (var reward in ResourceRewards)
		{
			for (int i = 0; i < reward.Quantity; i++)
			{
				Vector2 offset = new Vector2(
					(float)(GD.Randf() - 0.5f) * 16.0f,
					(float)(GD.Randf() - 0.5f) * 16.0f
				);
				Vector2 spawnPosition = GlobalPosition + offset;

				DroppedResource.SpawnResource(spawnPosition, reward.ResourceType, 1);
			}
		}
	}

	private void DestroyChest()
	{
		UnregisterFromGameData();
		QueueFree();
	}

	private void RegisterWithGameData()
	{
		var chestData = GetSaveData();
		GameSaveData.Instance.WorldData.Chests.Add(chestData);
		GD.Print($"Chest: Registered chest {Id} with GameData");
	}

	private void UnregisterFromGameData()
	{
		int removedCount = GameSaveData.Instance.WorldData.Chests.RemoveAll(c => c.Id == Id);
		GD.Print($"Chest: Unregistered chest {Id} from GameData (removed: {removedCount})");
	}

	public ChestSaveData GetSaveData()
	{
		return new ChestSaveData
		{
			Id = Id,
			X = GlobalPosition.X,
			Y = GlobalPosition.Y,
			ChestType = (int)CurrentChestType,
			GoldAmount = GoldAmount,
			ResourceRewards = new List<ResourceReward>(ResourceRewards),
			IsOpened = _isOpened
		};
	}

	public void LoadFromSaveData(ChestSaveData data)
	{
		Id = data.Id;
		GlobalPosition = new Vector2(data.X, data.Y);
		CurrentChestType = (ChestType)data.ChestType;
		GoldAmount = data.GoldAmount;
		ResourceRewards = new List<ResourceReward>(data.ResourceRewards);
		_isOpened = data.IsOpened;

		SetChestTexture();

		if (_isOpened)
		{
			QueueFree();
		}
	}

	public static Chest SpawnChest(Vector2 position, ChestType chestType, int goldAmount, List<ResourceReward> rewards = null)
	{
		var chestScene = GD.Load<PackedScene>("res://scenes/mapObjects/Chest.tscn");
		if (chestScene == null)
		{
			GD.PrintErr("Chest: Could not load Chest.tscn");
			return null;
		}

		var chest = chestScene.Instantiate<Chest>();
		if (chest == null)
		{
			GD.PrintErr("Chest: Failed to instantiate Chest");
			return null;
		}

		chest.GlobalPosition = position;
		chest.CurrentChestType = chestType;
		chest.GoldAmount = goldAmount;
		if (rewards != null)
		{
			chest.ResourceRewards = new List<ResourceReward>(rewards);
		}

		var currentScene = Engine.GetMainLoop() as SceneTree;
		currentScene?.CurrentScene?.CallDeferred(Node.MethodName.AddChild, chest);

		return chest;
	}
}
